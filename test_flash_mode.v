`timescale 1ns / 1ps

module test_flash_mode;

// Inputs
reg sysclk;
reg rstn;
reg load;
reg [15:0] cycle;
reg [15:0] period;
reg [15:0] step;
reg [15:0] flash_on_time;
reg [15:0] flash_off_time;
reg [7:0] flash_count;
reg [1:0] mode;

// Outputs
wire out;

// Instantiate the Unit Under Test (UUT)
breathLED uut (
    .sysclk(sysclk), 
    .rstn(rstn), 
    .out(out), 
    .mode(mode), 
    .load(load), 
    .cycle(cycle), 
    .period(period), 
    .step(step), 
    .flash_on_time(flash_on_time), 
    .flash_off_time(flash_off_time), 
    .flash_count(flash_count)
);

// Clock generation
initial begin
    sysclk = 0;
    forever #5 sysclk = ~sysclk; // 100MHz clock
end

// Test sequence
initial begin
    // Initialize Inputs
    rstn = 0;
    load = 0;
    cycle = 99;           // Small cycle for fast simulation
    period = 9;
    step = 0;
    flash_on_time = 10;   // Flash on for 10 PWM cycles
    flash_off_time = 5;   // Flash off for 5 PWM cycles
    flash_count = 3;      // Flash 3 times
    mode = 2'b00;         // OFF mode initially

    // Reset
    #100;
    rstn = 1;
    #50;

    // Load parameters
    load = 1;
    #20;
    load = 0;
    #50;

    // Test FLASH mode
    $display("Starting FLASH mode test at time %t", $time);
    mode = 2'b10; // FLASH mode
    
    // Wait for flash sequence to complete
    #50000;
    
    // Switch to OFF mode
    mode = 2'b00;
    #1000;
    
    // Switch back to FLASH mode to test reset
    $display("Testing FLASH mode reset at time %t", $time);
    mode = 2'b10;
    #50000;

    $display("Test completed at time %t", $time);
    $finish;
end

// Monitor output changes
always @(posedge sysclk) begin
    if (mode == 2'b10) begin // Only monitor in FLASH mode
        $display("Time: %t, out: %b, flash_counter: %d, flash_state: %b, flash_timer: %d", 
                 $time, out, uut.flash_counter, uut.flash_state, uut.flash_timer);
    end
end

endmodule
