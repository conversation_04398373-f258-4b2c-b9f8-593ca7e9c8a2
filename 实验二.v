//`define SIMULATION      // define SIM<PERSON><PERSON><PERSON>ON to speed up simulation
module breathLED(sysclk, rstn, out, mode, load, cycle, period, step, flash_on_time, flash_off_time, flash_count);
input  sysclk, rstn;    // 
input  load;            // on 'load' rising edge, update xxxx_value regs with cycle/value/step interface value
input  [15:0] cycle;    // C: pwm cycle max counts, pwm pulse freq F = [sysclk/(C+1)^2]
input  [15:0] period;   // P: every P+1 pwm cycles, update pwm_duty once, single dir breath freq F0=F/(P+1)
input  [15:0] step;     // S: how much add to pwm duty once P pwm cycles elapsed, breath freq F1= (S+1)*F0=(S+1)*F/(P+1)=sysclk*(S+1)/(C+1)^2/(P+1)
input  [15:0] flash_on_time;  //all-on time while flashing;
input  [15:0] flash_off_time;  //all-off time while flashing;
input  [7:0] flash_count;  //flash numbers;
input  [1:0] mode;   //different number to choose different mode;
output reg out;         // output connect to led

`ifndef SIMULATION      // NORMAL work
parameter CYCLE_INIT = 500-1;   // C: counts of each pwm cycle, pwm freq F=sysclk/([C+1)^2]=125MHz/250000=500Hz
parameter PERIOD_INIT = 1000-1; // P: one dir breath Time=1/F0: e.g. F0=F/(P+1)=F/1000 = 0.5Hz
parameter STEP_INIT = 1-1;      // S: duty cycle update step, one dir breath freq F1=(S+1)*F0, for this instance freq=0.5s
`else                   // IN SIMULATION TOOLs
parameter CYCLE_INIT = 100-1;
parameter PERIOD_INIT = 10-1;
parameter STEP_INIT = 1-1;
`endif

parameter OFF = 0;
parameter BREATH = 1;
parameter FLASH= 2;
parameter ON = 3;

wire  [7:0] flash_count = 5;
wire  [31:0] flash_on_time = 250000;
wire  [31:0] flash_off_time = 250000;
wire  [1:0] mode ;

reg [15:0] cycle_value;     // local copy of 'cycle' input when load trigger
reg [15:0] period_value;    // local copy of 'period' input when load trigger
reg [15:0] step_value;      // local copy of 'step' input when load trigger

reg [15:0] pwm_counter;     // counter for pwm generation
reg [15:0] pwm_duty;        // current pwm duty cycle, when pwm_counter<pwm_duty, out=1; otherwise out=0. every period_value pwm cycles, change pwm_duty once
reg [15:0] period_count;    // elapsed pwm cycles before change pwm_duty, when period_count>=period_value, change pwm_duty (+ or -)

reg flashflag;       // to panduan flash or not
reg [32:0] flashnum;      // to count flashtime 
reg [15:0] countnum;      // to count flash numbers

reg newvalue;               // each 'load' RE update value reg and set newvalue flag, FE clear newvalue flag
reg fade_dir;               // 0 = fade in, 1 = fade out 

    
always @(posedge sysclk) begin
    if (!rstn) begin
        newvalue <= 0;
        fade_dir <= 0;
        cycle_value <= CYCLE_INIT;
        period_value <= PERIOD_INIT;              
        step_value <= STEP_INIT;                  
        pwm_duty <= 0;
        period_count <= 0;
        pwm_counter <= 0;
        out <= 0;
        flashflag <= 1;
        flashnum <= 0;
        countnum <= 0;
    end 
        if (load && (!newvalue)) begin
            cycle_value <= cycle;
            period_value <= period;
            step_value <= step;
            newvalue <= 1;
        end else if ((!load) && newvalue) begin
            newvalue <= 0;
        end
    else if (mode == BREATH) begin
        

        if (pwm_counter < pwm_duty) begin
            out <= 1;
        end else begin
            out <= 0;
        end

        if (((pwm_counter + 1'b1) > pwm_counter) && (pwm_counter < cycle_value)) begin
            pwm_counter <= pwm_counter + 1'b1;  
        end else begin
            pwm_counter <= 0;

            if (fade_dir == 0) begin
                if (period_count < period_value) begin
                    period_count <= period_count + 1;
                end else begin
                    period_count <= 0;
                    if ((pwm_duty >= cycle_value) || (pwm_duty + step_value + 1 < pwm_duty)) begin
                        pwm_duty <= cycle_value;
                        fade_dir <= 1;              
                    end else begin
                        pwm_duty <= pwm_duty + step_value + 1;   
                    end
                end
            end else begin
                if (period_count < period_value) begin
                    period_count <= period_count + 1;
                end else begin
                    period_count <= 0;
                    if (pwm_duty > step_value + 1) begin
                        pwm_duty <= pwm_duty - step_value - 1;   
                    end else begin
                        pwm_duty <= 0;
                        fade_dir <= 0;              
                    end
                end
            end
        end
    end else if (mode == ON) begin                     //all-on mode
        out <= 1;
        countnum <= 0;
        pwm_counter <= 0;
        flashnum <= 0;
        flashflag <= 1;
    end else if (mode == OFF) begin                     //all-off mode
        out <= 0;
    end else if (mode == FLASH) begin                     
        if (((pwm_counter + 1'b1) > pwm_counter) && (pwm_counter < cycle_value)) begin
            pwm_counter <= pwm_counter + 1'b1;  
        end else begin
            pwm_counter <= 0;
            flashnum <= flashnum + 1;

            if (flashflag == 1) begin                                      //flash_on
                out <= 1;
                if (flashnum >= flash_on_time) begin
                    flashnum <= 0;
                    flashflag <= 0;
                end
            end else begin                                                  //flash_off
                out <= 0;
                if (flashnum >= flash_off_time) begin
                    flashnum <= 0;
                    flashflag <= 1;
                    countnum <= countnum + 1;
                end
            end

            if (countnum >= flash_count) begin
                out <= 0;
            end
        end
    end
end

endmodule