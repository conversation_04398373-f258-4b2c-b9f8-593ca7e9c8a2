//`define SIMULATION      // define SIM<PERSON><PERSON><PERSON>ON to speed up simulation
module breathLED(sysclk, rstn, out, mode, load, cycle, period, step, top, bottom, flash_on_time, flash_off_time, flash_count);
input  sysclk, rstn;
input  load;             // on 'load' rising edge, update xxxx_value regs with cycle/value/step interface value
input  [15:0] cycle;     // C: pwm cycle max counts, pwm pulse freq F = [sysclk/(C+1)^2]
input  [15:0] period;    // P: every P+1 pwm cycles, update pwm_duty once, single dir breath freq F0=F/(P+1)
input  [15:0] step;      // S: how much add to pwm duty once P pwm cycles elapsed, breath freq F1= (S+1)*F0=(S+1)*F/(P+1)=sysclk*(S+1)/(C+1)^2*(P+1)
input  [15:0] top;       // TOP: waiting time at 100% duty cycle, in PWM cycles
input  [15:0] bottom;    // BOT: waiting time at 0% duty cycle, in PWM cycles
input  [15:0] flash_on_time;   // all-on time while flashing, in PWM cycles
input  [15:0] flash_off_time;  // all-off time while flashing, in PWM cycles
input  [7:0] flash_count;      // flash numbers
input  [1:0] mode;       // different number to choose different mode
output reg out;          // output connect to led

`ifndef SIMULATION      // NORMAL work
parameter CYCLE_INIT = 500-1;   // C: counts of each pwm cycle, pwm freq F=sysclk/([C+1)^2]=125MHz/250000=500Hz
parameter PERIOD_INIT = 1000-1; // P: one dir breath Time=1/F0: e.g. F0=F/(P+1)=F/1000 = 0.5Hz
parameter STEP_INIT = 1-1;      // S: duty cycle update step, one dir breath freq F1=(S+1)*F0, for this instance freq=0.5s
parameter TOP_INIT = 500-1;     // TOP: default waiting time at 100% duty cycle (500 PWM cycles)
parameter BOT_INIT = 500-1;     // BOT: default waiting time at 0% duty cycle (500 PWM cycles)
`else                   // IN SIMULATION TOOLs
parameter CYCLE_INIT = 100-1;
parameter PERIOD_INIT = 10-1;
parameter STEP_INIT = 1-1;
parameter TOP_INIT = 10-1;
parameter BOT_INIT = 50-1;
`endif

// Mode definitions
parameter OFF = 2'b00;
parameter BREATH = 2'b01;
parameter FLASH = 2'b10;
parameter ON = 2'b11;

reg [15:0] cycle_value;     // local copy of 'cycle' input when load trigger
reg [15:0] period_value;    // local copy of 'period' input when load trigger
reg [15:0] step_value;      // local copy of 'step' input when load trigger
reg [15:0] top_value;       // local copy of 'top' input when load trigger
reg [15:0] bottom_value;    // local copy of 'bottom' input when load trigger

reg [15:0] pwm_counter;     // counter for pwm generation
reg [15:0] pwm_duty;        // current pwm duty cycle, when pwm_counter<pwm_duty, out=1; otherwise out=0. every period_value pwm cycles, change pwm_duty once
reg [15:0] period_count;    // elapsed pwm cycles before change pwm_duty, when period_count>=period_value, change pwm_duty (+ or -)
reg [15:0] pause_count;     // counter for pause time at top/bottom brightness

reg flashflag;              // to judge flash state: 1=flash_on, 0=flash_off
reg [31:0] flashnum;        // to count flash time (PWM cycles)
reg [15:0] countnum;        // to count flash numbers completed

reg newvalue;               // each 'load' RE update value reg and set newvalue flag, FE clear newvalue flag
reg fade_dir;               // 0 = fade in, 1 = fade out
reg pausing;                // 1 = currently in pause state (at top or bottom)

always @(posedge sysclk) begin
    if (!rstn) begin
        newvalue <= 0;
        fade_dir <= 0;
        cycle_value <= CYCLE_INIT;
        period_value <= PERIOD_INIT;
        step_value <= STEP_INIT;
        top_value <= TOP_INIT;
        bottom_value <= BOT_INIT;
        pwm_duty <= 0;
        period_count <= 0;
        pwm_counter <= 0;
        pause_count <= 0;
        pausing <= 0;
        out <= 0;
        flashflag <= 1;        // Start with flash ON state
        flashnum <= 0;
        countnum <= 0;
    end else begin
        // Load new parameters
        if (load && (!newvalue)) begin
            cycle_value <= cycle;
            period_value <= period;
            step_value <= step;
            top_value <= top;
            bottom_value <= bottom;
            newvalue <= 1;
        end else if ((!load) && newvalue) begin
            newvalue <= 0;
        end

        // Mode control logic
        if (mode == BREATH) begin
            // Breathing mode - PWM output control
            if (pwm_counter < pwm_duty) begin
                out <= 1;
            end else begin
                out <= 0;
            end

            // PWM counter and breathing logic
            if (((pwm_counter + 1'b1) > pwm_counter) && (pwm_counter < cycle_value)) begin
                pwm_counter <= pwm_counter + 1'b1;
            end else begin
                pwm_counter <= 0;

                // Breathing duty cycle control
                if (pausing) begin
                    // Handle pausing at top or bottom brightness
                    if ((fade_dir == 0 && pause_count < top_value) ||
                        (fade_dir == 1 && pause_count < bottom_value)) begin
                        pause_count <= pause_count + 1;   // Continue pausing
                    end else begin
                        pause_count <= 0;
                        pausing <= 0;   // End of pause
                        fade_dir <= ~fade_dir;   // Change direction after pause completes
                    end
                end else if (fade_dir == 0) begin    // fade in, pwm duty increase direction
                    if (period_count < period_value) begin
                        period_count <= period_count + 1;
                    end else begin
                        period_count <= 0;
                        if ((pwm_duty >= cycle_value) || (pwm_duty + step_value + 1 < pwm_duty)) begin  // check overflow
                            pwm_duty <= cycle_value;
                            if (top_value > 0) begin
                                pausing <= 1;     // Start pausing at top brightness
                                pause_count <= 0;
                            end else begin
                                fade_dir <= 1;    // No pause needed, change direction immediately
                            end
                        end else begin
                            pwm_duty <= pwm_duty + step_value + 1;   // increase pwm_duty when fade in
                        end
                    end
                end else begin               // fade_dir = 1, fade out direction
                    if (period_count < period_value) begin
                        period_count <= period_count + 1;
                    end else begin
                        period_count <= 0;
                        if (pwm_duty > step_value + 1) begin
                            pwm_duty <= pwm_duty - step_value - 1;   // decrease pwm_duty to fade out
                        end else begin
                            pwm_duty <= 0;
                            if (bottom_value > 0) begin
                                pausing <= 1;     // Start pausing at bottom brightness
                                pause_count <= 0;
                            end else begin
                                fade_dir <= 0;    // No pause needed, change direction immediately
                            end
                        end
                    end
                end
            end
        end else if (mode == ON) begin                     // all-on mode
            out <= 1;
            countnum <= 0;
            pwm_counter <= 0;
            flashnum <= 0;
            flashflag <= 1;
        end else if (mode == OFF) begin                     // all-off mode
            out <= 0;
            countnum <= 0;
            pwm_counter <= 0;
            flashnum <= 0;
            flashflag <= 1;
        end else if (mode == FLASH) begin                     // flash mode
            if (((pwm_counter + 1'b1) > pwm_counter) && (pwm_counter < cycle_value)) begin
                pwm_counter <= pwm_counter + 1'b1;
            end else begin
                pwm_counter <= 0;
                flashnum <= flashnum + 1;

                if (flashflag == 1) begin                                      // flash_on
                    out <= 1;
                    if (flashnum >= flash_on_time) begin
                        flashnum <= 0;
                        flashflag <= 0;
                    end
                end else begin                                                  // flash_off
                    out <= 0;
                    if (flashnum >= flash_off_time) begin
                        flashnum <= 0;
                        flashflag <= 1;
                        countnum <= countnum + 1;
                    end
                end

                if (countnum >= flash_count) begin
                    out <= 0;
                end
            end
        end
    end
end

endmodule
