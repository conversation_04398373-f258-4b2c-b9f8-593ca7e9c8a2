//`define SIMULATION      // define SIM<PERSON>LATION to speed up simulation
module breathLED(sysclk, rstn, out, load, cycle, period, step);
input  sysclk, rstn;    // 
input  load;            // on 'load' rising edge, update xxxx_value regs with cycle/value/step interface value
input  [15:0] cycle;    // C: pwm cycle max counts, pwm pulse freq F = [sysclk/(C+1)^2]
input  [15:0] period;   // P: every P+1 pwm cycles, update pwm_duty once, single dir breath freq F0=F/(P+1)
input  [15:0] step;     // S: how much add to pwm duty once P pwm cycles elapsed, breath freq F1= (S+1)*F0=(S+1)*F/(P+1)=sysclk*(S+1)/(C+1)^2/(P+1)
output reg out;         // output connect to led

`ifndef SIMULATION      // NORMAL work
parameter CYCLE_INIT = 500-1;   // C: counts of each pwm cycle, pwm freq F=sysclk/([C+1)^2]=125MHz/250000=500Hz
parameter PERIOD_INIT = 1000-1; // P: one dir breath Time=1/F0: e.g. F0=F/(P+1)=F/1000 = 0.5Hz
parameter STEP_INIT = 1-1;      // S: duty cycle update step, one dir breath freq F1=(S+1)*F0, for this instance freq=0.5s
`else                   // IN SIMULATION TOOLs
parameter CYCLE_INIT = 100-1;
parameter PERIOD_INIT = 10-1;
parameter STEP_INIT = 1-1;
`endif

reg [15:0] cycle_value;     // local copy of 'cycle' input when load trigger
reg [15:0] period_value;    // local copy of 'period' input when load trigger
reg [15:0] step_value;      // local copy of 'step' input when load trigger

reg [15:0] pwm_counter;     // counter for pwm generation
reg [15:0] pwm_duty;        // current pwm duty cycle, when pwm_counter<pwm_duty, out=1; otherwise out=0. every period_value pwm cycles, change pwm_duty once
reg [15:0] period_count;    // elapsed pwm cycles before change pwm_duty, when period_count>=period_value, change pwm_duty (+ or -)

reg newvalue;               // each 'load' RE update value reg and set newvalue flag, FE clear newvalue flag
reg fade_dir;               // 0 = fade in, 1 = fade out 

always @(posedge sysclk) begin
    if (!rstn) begin
        newvalue <= 0;
        fade_dir <= 0;
        cycle_value <= CYCLE_INIT;
        period_value <= PERIOD_INIT;
        step_value <= STEP_INIT;
        pwm_duty <= 0;
        period_count <= 0;
        pwm_counter <= 0;
        out <= 0;
    end
    else begin
        if (load && (!newvalue)) begin  // load new value to xxxx_value reg from interface
            cycle_value <= cycle;
            period_value <= period;
            step_value <= step;
            newvalue <= 1;
        end
        else if ((!load) && newvalue)   //after load clear, clear newvalue flag
            newvalue <= 0;
        
        if (pwm_counter < pwm_duty)     //from 0 to pwm_duty, out=1, if pwm_duty=0, keep out=0
            out <= 1;
        else                //from pwm_duty to cycle_value, out=0
            out <= 0;
            
        // PWM count in single pwm cycle: check counter overflow
        if (((pwm_counter + 1'b1) > pwm_counter) && (pwm_counter < cycle_value))   // normal count
            pwm_counter <= pwm_counter + 1'b1;  
        else begin                  // overflow, fold back to 0, current pwm cycle finished, check duty + -
            pwm_counter <= 0;

            // after 'period_value' pwm cycles, update pwm_duty to new duty cycle%
            if (fade_dir == 0) begin    //fade in, pwm duty increase direction
                if (period_count < period_value) begin
                    period_count <= period_count + 1;
                end
                else begin
                    period_count <= 0;
                    if ((pwm_duty >= cycle_value) || (pwm_duty + step_value + 1 < pwm_duty)) begin  //check overflow
                        pwm_duty <= cycle_value;
                        fade_dir <= 1;              //fade in finished, change to fade out direction
                    end
                    else
                        pwm_duty <= pwm_duty + step_value + 1;   //increase pwm_duty when fade in
                end
            end
            else begin              // fade_dir = 1, fade out direction
                if (period_count < period_value) begin
                    period_count <= period_count + 1;
                end
                else begin
                    period_count <= 0;
                    if (pwm_duty > step_value + 1)
                        pwm_duty <= pwm_duty - step_value - 1;   //decrease pwm_duty to fade out
                    else begin
                        pwm_duty <= 0;
                        fade_dir <= 0;              //fade out finished, change to fade in direction
                    end
                end
            end
        end
    end
end

endmodule
分析代码