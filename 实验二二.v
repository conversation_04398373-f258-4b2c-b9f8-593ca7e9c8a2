//`define SIMULATION      // define <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to speed up simulation
module breathLED(sysclk, rstn, out, mode, load, cycle, period, step, flash_on_time, flash_off_time, flash_count);
input  sysclk, rstn;    //
input  load;            // on 'load' rising edge, update xxxx_value regs with cycle/value/step interface value
input  [15:0] cycle;    // C: pwm cycle max counts, pwm pulse freq F = [sysclk/(C+1)^2]
input  [15:0] period;   // P: every P+1 pwm cycles, update pwm_duty once, single dir breath freq F0=F/(P+1)
input  [15:0] step;     // S: how much add to pwm duty once P pwm cycles elapsed, breath freq F1= (S+1)*F0=(S+1)*F/(P+1)=sysclk*(S+1)/(C+1)^2/(P+1)
input  [15:0] flash_on_time;  // all-on time while flashing
input  [15:0] flash_off_time; // all-off time while flashing
input  [7:0] flash_count;     // flash numbers
input  [1:0] mode;            // different number to choose different mode
output reg out;               // output connect to led

`ifndef SIMULATION      // NORMAL work
parameter CYCLE_INIT = 500-1;   // C: counts of each pwm cycle, pwm freq F=sysclk/([C+1)^2]=125MHz/250000=500Hz
parameter PERIOD_INIT = 1000-1; // P: one dir breath Time=1/F0: e.g. F0=F/(P+1)=F/1000 = 0.5Hz
parameter STEP_INIT = 1-1;      // S: duty cycle update step, one dir breath freq F1=(S+1)*F0, for this instance freq=0.5s
`else                   // IN SIMULATION TOOLs
parameter CYCLE_INIT = 100-1;
parameter PERIOD_INIT = 10-1;
parameter STEP_INIT = 1-1;
`endif

// Mode parameters
parameter OFF = 2'b00;
parameter BREATH = 2'b01;
parameter FLASH = 2'b10;
parameter ON = 2'b11;

reg [15:0] cycle_value;       // local copy of 'cycle' input when load trigger
reg [15:0] period_value;      // local copy of 'period' input when load trigger
reg [15:0] step_value;        // local copy of 'step' input when load trigger
reg [15:0] flash_on_value;    // local copy of flash_on_time
reg [15:0] flash_off_value;   // local copy of flash_off_time
reg [7:0] flash_count_value;  // local copy of flash_count

reg [15:0] pwm_counter;       // counter for pwm generation
reg [15:0] pwm_duty;          // current pwm duty cycle, when pwm_counter<pwm_duty, out=1; otherwise out=0. every period_value pwm cycles, change pwm_duty once
reg [15:0] period_count;      // elapsed pwm cycles before change pwm_duty, when period_count>=period_value, change pwm_duty (+ or -)

// Flash mode registers
reg flash_state;              // 0 = flash_on, 1 = flash_off
reg [31:0] flash_timer;       // timer for flash on/off timing
reg [7:0] flash_counter;      // counter for flash numbers

reg newvalue;                 // each 'load' RE update value reg and set newvalue flag, FE clear newvalue flag
reg fade_dir;                 // 0 = fade in, 1 = fade out

always @(posedge sysclk) begin
    if (!rstn) begin
        // Reset all registers
        newvalue <= 0;
        fade_dir <= 0;
        cycle_value <= CYCLE_INIT;
        period_value <= PERIOD_INIT;
        step_value <= STEP_INIT;
        flash_on_value <= 250000;    // Default flash on time
        flash_off_value <= 250000;   // Default flash off time
        flash_count_value <= 5;      // Default flash count
        pwm_duty <= 0;
        period_count <= 0;
        pwm_counter <= 0;
        out <= 0;
        flash_state <= 0;
        flash_timer <= 0;
        flash_counter <= 0;
    end
    else begin
        // Load new parameters when load signal is active
        if (load && (!newvalue)) begin
            cycle_value <= cycle;
            period_value <= period;
            step_value <= step;
            flash_on_value <= flash_on_time;
            flash_off_value <= flash_off_time;
            flash_count_value <= flash_count;
            newvalue <= 1;
        end
        else if ((!load) && newvalue) begin
            newvalue <= 0;
        end

        // Mode selection logic
        case (mode)
            OFF: begin
                out <= 0;
                // Reset counters for other modes
                pwm_counter <= 0;
                flash_timer <= 0;
                flash_counter <= 0;
                flash_state <= 0;
            end

            ON: begin
                out <= 1;
                // Reset counters for other modes
                pwm_counter <= 0;
                flash_timer <= 0;
                flash_counter <= 0;
                flash_state <= 0;
            end

            BREATH: begin
                // PWM generation for breathing effect
                if (pwm_counter < pwm_duty) begin
                    out <= 1;
                end else begin
                    out <= 0;
                end

                // PWM counter management
                if (pwm_counter < cycle_value) begin
                    pwm_counter <= pwm_counter + 1;
                end else begin
                    pwm_counter <= 0;

                    // Breathing logic - update duty cycle
                    if (fade_dir == 0) begin    // Fade in
                        if (period_count < period_value) begin
                            period_count <= period_count + 1;
                        end else begin
                            period_count <= 0;
                            if (pwm_duty >= cycle_value) begin
                                pwm_duty <= cycle_value;
                                fade_dir <= 1;  // Switch to fade out
                            end else begin
                                pwm_duty <= pwm_duty + step_value + 1;
                            end
                        end
                    end else begin              // Fade out
                        if (period_count < period_value) begin
                            period_count <= period_count + 1;
                        end else begin
                            period_count <= 0;
                            if (pwm_duty <= step_value + 1) begin
                                pwm_duty <= 0;
                                fade_dir <= 0;  // Switch to fade in
                            end else begin
                                pwm_duty <= pwm_duty - step_value - 1;
                            end
                        end
                    end
                end

                // Reset flash counters
                flash_timer <= 0;
                flash_counter <= 0;
                flash_state <= 0;
            end

            FLASH: begin
                // Flash mode logic
                if (flash_counter < flash_count_value) begin
                    if (flash_state == 0) begin  // Flash ON state
                        out <= 1;
                        if (flash_timer < flash_on_value) begin
                            flash_timer <= flash_timer + 1;
                        end else begin
                            flash_timer <= 0;
                            flash_state <= 1;  // Switch to OFF state
                        end
                    end else begin  // Flash OFF state
                        out <= 0;
                        if (flash_timer < flash_off_value) begin
                            flash_timer <= flash_timer + 1;
                        end else begin
                            flash_timer <= 0;
                            flash_state <= 0;  // Switch to ON state
                            flash_counter <= flash_counter + 1;
                        end
                    end
                end else begin
                    // Flash sequence completed
                    out <= 0;
                end

                // Reset breathing counters
                pwm_counter <= 0;
                period_count <= 0;
                pwm_duty <= 0;
                fade_dir <= 0;
            end

            default: begin
                out <= 0;
            end
        endcase
    end
end

endmodule